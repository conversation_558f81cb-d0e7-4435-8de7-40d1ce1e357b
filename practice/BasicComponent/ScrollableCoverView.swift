import SwiftUI

struct ScrollableCoverView<Content: View, Background: View, HeaderView: View>: View {
    @State private var scrollOffset: CGFloat = 0
    @State private var lastUpdateTime: Date = Date()
    private var imageHeight: CGFloat = 300
    private let updateThreshold: TimeInterval = 0.010
    
    var title: String
    var header: () -> HeaderView
    var background: () -> Background
    var content: () -> Content
    
    init(title: String, header: @escaping () -> HeaderView, background: @escaping () -> Background, @ViewBuilder content: @escaping () -> Content ) {
        self.title = title
        self.header = header
        self.background = background
        self.content = content
    }

    var body: some View {
        GeometryReader { geo in
            let frameHeight = min(imageHeight, geo.size.width)
            ZStack {
                VStack(spacing: 0) {
                    background()
                        .frame(width: geo.size.width, height: frameHeight, alignment: .center)
                        .clipped()
                        .overlay(content: {
                            Rectangle()
                                .fill(.black)
                                .opacity(max(0, scrollOffset) / (0.8 * frameHeight))
                        })
                    Spacer()
                }
                .offset(y: -scrollOffset)
                .edgesIgnoringSafeArea(.top)
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: 0) {
                        Rectangle()
                            .fill(.clear)
                            .frame(width: geo.size.width, height: frameHeight)
                            .overlay(content: header)
                        VStack(spacing: 0) {
                            content()
                        }
                        .padding(0)
                        .frame(width: geo.size.width)
                    }
                    .padding(0)
                    .background(GeometryReader {
                        Color.clear.preference(key: ScrollOffsetKey.self, value: -$0.frame(in: .named("scroll")).origin.y)
                    })
                    .onPreferenceChange(ScrollOffsetKey.self) { newOffset in
                        let now = Date()
                        if now.timeIntervalSince(lastUpdateTime) >= updateThreshold {
                            scrollOffset = newOffset
                            lastUpdateTime = now
                        }
                    }
                }
//                .background(.red)
                .coordinateSpace(name: "scroll")
                .edgesIgnoringSafeArea(.top)
            }
            .navigationTitle(scrollOffset > (frameHeight - 50) ? title : "")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// PreferenceKey for Scroll Offset
struct ScrollOffsetKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value += nextValue()
    }
}

struct ScrollableCoverView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ScrollableCoverView(
                title: "巴赫",
                header: {
                    EmptyView()
                },
                background: {
                    LinearGradient(
                        gradient: Gradient(colors: [Color.gray.opacity(0.4), Color.gray.opacity(0.2)]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                }
            ) {
                VStack (spacing: 0) {
                    HStack (alignment: .top, spacing: 0) {
                        Text("fadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;a")
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                    
                    HStack (alignment: .top) {
                        Text("fadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;afadfjlkajsdfl;jal;ksdfjal;kdsjflk;a")
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
                .padding(0)
                .background(Color(.secondarySystemBackground))
            }
        }
    }
}
