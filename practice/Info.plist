<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSUserNotificationUsageDescription</key>
	<string>应用需要发送通知来提醒您计时器结束</string>
	<key>NSUserNotificationAlertUsageDescription</key>
	<string>App 需要使用通知来及时提醒你即将发生的事项</string>
	<key>NSCriticalAlertUsageDescription</key>
	<string>用于倒计时提醒，确保重要提醒能播放</string>
	<key>UNNotificationAlertStyle</key>
	<string>critical</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>Allow notifications to remind practice</string>
	<key>UIAppFonts</key>
	<array>
		<string>Nunito-SemiBold.ttf</string>
		<string>Nunito-Bold.ttf</string>
		<string>Nunito-Regular.ttf</string>
	</array>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need access to save your achievement image.</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>xhsdiscover</string>
	</array>
	<key>NSMicrophoneUsageDescription</key>
	<string>Need Microphone to record practice</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Get video to import</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>audio</string>
		<string>fetch</string>
	</array>
</dict>
</plist>
