//
//  BackupManager.swift
//  practice
//
//  Created by Augment Agent on 2025/6/28.
//

import Foundation
import SwiftData
import SwiftUI
import Compression
import System
import UniformTypeIdentifiers

// 临时VideoItem类型别名，因为在practiceApp.swift中被注释掉了
typealias VideoItem = TaskItemSchemaV1.VideoItem

@MainActor
class BackupManager: ObservableObject {
    private let modelContext: ModelContext = TaskItemSchemaV1.container.mainContext
    private lazy var imageManager = ImageManager(modelContext: modelContext)
    private lazy var recordingStorage = RecordingManager.defaultStorageProvider()
    
    @Published var isBackingUp = false
    @Published var isImporting = false
    @Published var backupProgress: Double = 0.0
    @Published var importProgress: Double = 0.0
    @Published var currentBackupStatus = ""
    @Published var currentImportStatus = ""
    @Published var importResults: ImportResults?
    
    struct ImportResults: Equatable {
        let successfulTasks: Int
        let failedTasks: Int
        let successfulCollections: Int
        let failedCollections: Int
        let errors: [String]
    }
    
    struct BackupData: Codable {
        let version: String
        let createdAt: Date
        let tasks: [TaskBackupData]
        let collections: [CollectionBackupData]
    }
    
    struct TaskBackupData: Codable {
        let id: UUID
        let pieceName: String
        let composerName: String
        let key: String
        let difficulty: String
        let beginDate: Date
        let finishedDate: Date
        let taskType: String?
        let lastModified: Date?
        let coverImagePath: String?
        let coverImageData: Data?
        let taskProgress: [TaskProgressBackupData]
        let recordings: [RecordingBackupData]
        let videos: [VideoBackupData]
    }
    
    struct TaskProgressBackupData: Codable {
        let date: Date
        let status: String
        let practiceTime: Int?
    }
    
    struct RecordingBackupData: Codable {
        let date: Date
        let fileUrl: String
        let isFavorite: Bool
        let note: String
        let audioData: Data?
    }
    
    struct VideoBackupData: Codable {
        let date: Date
        let fileUrl: String
        let videoData: Data?
    }
    
    struct CollectionBackupData: Codable {
        let id: UUID
        let cover: String
        let name: String
        let desc: String
        let order: Int
        let type: String
        let createdDate: Date
        let lastModified: Date
        let coverImageData: Data?
        let taskIds: [UUID]
    }
    
    // MARK: - Backup Functions
    
    func startBackup() async throws -> URL {
        await MainActor.run {
            isBackingUp = true
            backupProgress = 0.0
            currentBackupStatus = "开始备份..."
        }
        
        do {
            // 1. 获取所有数据
            let tasks = try await getAllTasks()
            let collections = try await getAllCollections()
            
            // 2. 创建备份数据
            let backupData = try await createBackupData(tasks: tasks, collections: collections)
            
            // 3. 保存到临时文件
            let backupURL = try await saveBackupToFile(backupData)
            
            await MainActor.run {
                isBackingUp = false
                currentBackupStatus = "备份完成"
            }
            
            return backupURL
        } catch {
            await MainActor.run {
                isBackingUp = false
                currentBackupStatus = "备份失败: \(error.localizedDescription)"
            }
            throw error
        }
    }
    
    private func getAllTasks() async throws -> [TaskItem] {
        await MainActor.run {
            currentBackupStatus = "正在获取任务数据..."
        }
        
        let descriptor = FetchDescriptor<TaskItem>()
        return try modelContext.fetch(descriptor)
    }
    
    private func getAllCollections() async throws -> [CollectionItem] {
        await MainActor.run {
            currentBackupStatus = "正在获取收藏集数据..."
        }
        
        let descriptor = FetchDescriptor<CollectionItem>()
        return try modelContext.fetch(descriptor)
    }
    
    private func createBackupData(tasks: [TaskItem], collections: [CollectionItem]) async throws -> BackupData {
        let totalItems = tasks.count + collections.count
        var processedItems = 0
        
        // 备份任务
        var taskBackupData: [TaskBackupData] = []
        for (index, task) in tasks.enumerated() {
            await MainActor.run {
                currentBackupStatus = "正在备份任务 \(index + 1)/\(tasks.count): \(task.pieceName)"
                backupProgress = Double(processedItems) / Double(totalItems)
            }
            
            let taskData = try await createTaskBackupData(task)
            taskBackupData.append(taskData)
            processedItems += 1
        }
        
        // 备份收藏集
        var collectionBackupData: [CollectionBackupData] = []
        for (index, collection) in collections.enumerated() {
            await MainActor.run {
                currentBackupStatus = "正在备份收藏集 \(index + 1)/\(collections.count): \(collection.name)"
                backupProgress = Double(processedItems) / Double(totalItems)
            }
            
            let collectionData = try await createCollectionBackupData(collection)
            collectionBackupData.append(collectionData)
            processedItems += 1
        }
        
        await MainActor.run {
            backupProgress = 1.0
        }
        
        return BackupData(
            version: "1.0",
            createdAt: Date(),
            tasks: taskBackupData,
            collections: collectionBackupData
        )
    }

    private func createTaskBackupData(_ task: TaskItem) async throws -> TaskBackupData {
        // 备份封面图片
        var coverImageData: Data?
        if let coverImagePath = task.coverImagePath {
            do {
                let imageURL = imageManager.storage.resolveURL(for: coverImagePath)
                coverImageData = try Data(contentsOf: imageURL)
            } catch {
                print("❌ 无法读取任务封面图片: \(error)")
            }
        }

        // 备份练习进度
        let progressData = (task.taskProgress ?? []).map { progress in
            TaskProgressBackupData(
                date: progress.date,
                status: progress.status == .notStart ? "notStart" : "finished",
                practiceTime: progress.practiceTime
            )
        }

        // 备份录音文件
        var recordingData: [RecordingBackupData] = []
        if let recordings = task.recordings {
            for (index, recording) in recordings.enumerated() {
                await MainActor.run {
                    currentBackupStatus = "正在备份录音 \(index + 1)/\(recordings.count)"
                }

                var audioData: Data?
                if !recording.fileUrl.isEmpty {
                    do {
                        let recordingURL = recordingStorage.resolveURL(for: recording.fileUrl)
                        audioData = try Data(contentsOf: recordingURL)
                    } catch {
                        print("❌ 无法读取录音文件: \(error)")
                    }
                }

                recordingData.append(RecordingBackupData(
                    date: recording.date,
                    fileUrl: recording.fileUrl,
                    isFavorite: recording.isFavorite,
                    note: recording.note,
                    audioData: audioData
                ))
            }
        }

        // 备份视频文件
        var videoData: [VideoBackupData] = []
        if let videos = task.videos {
            for (index, video) in videos.enumerated() {
                await MainActor.run {
                    currentBackupStatus = "正在备份视频 \(index + 1)/\(videos.count)"
                }

                var videoFileData: Data?
                if !video.fileUrl.isEmpty {
                    do {
                        let videoURL = recordingStorage.resolveURL(for: video.fileUrl)
                        videoFileData = try Data(contentsOf: videoURL)
                    } catch {
                        print("❌ 无法读取视频文件: \(error)")
                    }
                }

                videoData.append(VideoBackupData(
                    date: video.date,
                    fileUrl: video.fileUrl,
                    videoData: videoFileData
                ))
            }
        }

        return TaskBackupData(
            id: task.id,
            pieceName: task.pieceName,
            composerName: task.composerName,
            key: task.key.rawValue,
            difficulty: task.difficulty.rawValue,
            beginDate: task.beginDate,
            finishedDate: task.finishedDate,
            taskType: task.taskType?.rawValue,
            lastModified: task.lastModified,
            coverImagePath: task.coverImagePath,
            coverImageData: coverImageData,
            taskProgress: progressData,
            recordings: recordingData,
            videos: videoData
        )
    }

    private func createCollectionBackupData(_ collection: CollectionItem) async throws -> CollectionBackupData {
        // 备份收藏集封面图片
        var coverImageData: Data?
        if !collection.cover.isEmpty {
            do {
                let imageURL = imageManager.storage.resolveURL(for: collection.cover)
                coverImageData = try Data(contentsOf: imageURL)
            } catch {
                print("❌ 无法读取收藏集封面图片: \(error)")
            }
        }

        // 获取关联的任务ID
        let taskIds = collection.taskItems?.map { $0.id } ?? []

        return CollectionBackupData(
            id: collection.id,
            cover: collection.cover,
            name: collection.name,
            desc: collection.desc,
            order: collection.order,
            type: collection.type.rawValue,
            createdDate: collection.createdDate,
            lastModified: collection.lastModified,
            coverImageData: coverImageData,
            taskIds: taskIds
        )
    }

    private func saveBackupToFile(_ backupData: BackupData) async throws -> URL {
        await MainActor.run {
            currentBackupStatus = "正在收集媒体文件..."
        }

        // 收集所有媒体文件
        let mediaFiles = try await collectMediaFiles(from: backupData)

        await MainActor.run {
            currentBackupStatus = "正在创建备份压缩包..."
        }

        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let jsonData = try encoder.encode(backupData)

        // 创建ZIP文件
        let zipURL = try createZipArchive(jsonData: jsonData, mediaFiles: mediaFiles)

        return zipURL
    }

    private func collectMediaFiles(from backupData: BackupData) async throws -> [MediaFileInfo] {
        var mediaFiles: [MediaFileInfo] = []
        // 获取图片存储目录
        let imageStorage = imageManager.storage

        // 收集任务封面图片
        for (index, taskData) in backupData.tasks.enumerated() {
            if let imageData = taskData.coverImageData {
                // 创建临时文件，使用索引确保唯一性
                let fileName = "task_cover_\(index)_\(taskData.id.uuidString).jpg"
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
                try imageData.write(to: tempURL)

                mediaFiles.append(MediaFileInfo(
                    sourceURL: tempURL,
                    fileName: fileName,
                    type: .image
                ))
            }

            // 收集录音文件
            for (recordingIndex, recordingData) in taskData.recordings.enumerated() {
                if let audioData = recordingData.audioData {
                    // 创建唯一的文件名
                    let fileName = "recording_\(index)_\(recordingIndex)_\(UUID().uuidString).m4a"
                    let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
                    try audioData.write(to: tempURL)

                    mediaFiles.append(MediaFileInfo(
                        sourceURL: tempURL,
                        fileName: fileName,
                        type: .recording
                    ))
                }
            }

            // 收集视频文件（如果有）
            for (videoIndex, videoItem) in taskData.videos.enumerated() {
                if let videoData = videoItem.videoData {
                    let fileName = "video_\(index)_\(videoIndex)_\(UUID().uuidString).mp4"
                    let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
                    try videoData.write(to: tempURL)

                    mediaFiles.append(MediaFileInfo(
                        sourceURL: tempURL,
                        fileName: fileName,
                        type: .video
                    ))
                }
            }
        }

        // 收集收藏集封面图片
        for (index, collectionData) in backupData.collections.enumerated() {
            // CollectionBackupData 只有 coverImageData，没有 coverImagePath
            // 如果有 coverImageData，我们需要创建临时文件
            if let imageData = collectionData.coverImageData {
                let fileName = "collection_cover_\(index)_\(collectionData.id.uuidString).jpg"
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
                try imageData.write(to: tempURL)

                mediaFiles.append(MediaFileInfo(
                    sourceURL: tempURL,
                    fileName: fileName,
                    type: .image
                ))
            }
        }

        return mediaFiles
    }

    private func cleanupTemporaryFiles(_ mediaFiles: [MediaFileInfo]) {
        for mediaFile in mediaFiles {
            do {
                if FileManager.default.fileExists(atPath: mediaFile.sourceURL.path) {
                    try FileManager.default.removeItem(at: mediaFile.sourceURL)
                }
            } catch {
                print("⚠️ 清理临时文件失败: \(mediaFile.fileName) - \(error)")
            }
        }
    }

    // MARK: - Import Functions

    func startImport(from fileURL: URL) async throws {
        await MainActor.run {
            isImporting = true
            importProgress = 0.0
            currentImportStatus = "开始导入..."
            importResults = nil
        }

        do {
            // 1. 验证文件格式
            let backupData = try await validateAndLoadBackupFile(fileURL)

            // 2. 执行导入
            let results = try await performImport(backupData)

            await MainActor.run {
                isImporting = false
                importResults = results
                currentImportStatus = "导入完成"
            }
        } catch {
            await MainActor.run {
                isImporting = false
                currentImportStatus = "导入失败: \(error.localizedDescription)"
            }
            throw error
        }
    }

    private func validateAndLoadBackupFile(_ fileURL: URL) async throws -> BackupData {
        await MainActor.run {
            currentImportStatus = "正在验证备份文件..."
        }

        let didAccess = fileURL.startAccessingSecurityScopedResource()
        defer {
            if didAccess {
                fileURL.stopAccessingSecurityScopedResource()
            }
        }

        guard fileURL.pathExtension.lowercased() == "zip" else {
            throw NSError(domain: "BackupError", code: 1001, userInfo: [NSLocalizedDescriptionKey: "文件格式不正确，请选择.zip格式的备份文件"])
        }

        // 解压ZIP文件
        let extractedData = try await extractZipFile(fileURL)
        return extractedData
    }

    private func performImport(_ backupData: BackupData) async throws -> ImportResults {
        let totalItems = backupData.tasks.count + backupData.collections.count
        var processedItems = 0
        var successfulTasks = 0
        var failedTasks = 0
        var successfulCollections = 0
        var failedCollections = 0
        var errors: [String] = []

        // 导入任务
        for (index, taskData) in backupData.tasks.enumerated() {
            await MainActor.run {
                currentImportStatus = "正在导入任务 \(index + 1)/\(backupData.tasks.count): \(taskData.pieceName)"
                importProgress = Double(processedItems) / Double(totalItems)
            }

            do {
                try await importTask(taskData)
                successfulTasks += 1
            } catch {
                failedTasks += 1
                errors.append("任务 '\(taskData.pieceName)': \(error.localizedDescription)")
            }

            processedItems += 1
        }

        // 导入收藏集
        for (index, collectionData) in backupData.collections.enumerated() {
            await MainActor.run {
                currentImportStatus = "正在导入收藏集 \(index + 1)/\(backupData.collections.count): \(collectionData.name)"
                importProgress = Double(processedItems) / Double(totalItems)
            }

            do {
                try await importCollection(collectionData, allTasks: backupData.tasks)
                successfulCollections += 1
            } catch {
                failedCollections += 1
                errors.append("收藏集 '\(collectionData.name)': \(error.localizedDescription)")
            }

            processedItems += 1
        }

        await MainActor.run {
            importProgress = 1.0
        }

        return ImportResults(
            successfulTasks: successfulTasks,
            failedTasks: failedTasks,
            successfulCollections: successfulCollections,
            failedCollections: failedCollections,
            errors: errors
        )
    }

    private func importTask(_ taskData: TaskBackupData) async throws {
        // 检查是否已存在相同ID的任务
        let existingTaskDescriptor = FetchDescriptor<TaskItem>(predicate: #Predicate { $0.id == taskData.id })
        let existingTasks = try modelContext.fetch(existingTaskDescriptor)

        if !existingTasks.isEmpty {
            throw NSError(domain: "ImportError", code: 2001, userInfo: [NSLocalizedDescriptionKey: "任务已存在"])
        }

        // 创建新任务
        let task = TaskItem(
            pieceName: taskData.pieceName,
            composerName: taskData.composerName,
            key: MusicalKey(rawValue: taskData.key) ?? .cMajor,
            difficulty: Level(rawValue: taskData.difficulty) ?? .one,
            beginDate: taskData.beginDate,
            taskType: taskData.taskType.flatMap { TaskType(rawValue: $0) } ?? .Ongoing
        )

        task.id = taskData.id
        task.finishedDate = taskData.finishedDate
        task.lastModified = taskData.lastModified

        // 导入封面图片
        if let coverImageData = taskData.coverImageData {
            do {
                let fileName = "\(task.id.uuidString)_cover.jpg"
                let imageURL = imageManager.storage.urlForSaving(filename: fileName)
                try coverImageData.write(to: imageURL)
                task.coverImagePath = fileName
            } catch {
                print("❌ 导入任务封面图片失败: \(error)")
            }
        }

        modelContext.insert(task)

        // 导入练习进度
        for progressData in taskData.taskProgress {
            let status: TaskStatus = progressData.status == "finished" ? .finished : .notStart
            let progress = TaskProgress(
                date: progressData.date,
                status: status
            )
            progress.practiceTime = progressData.practiceTime
            progress.task = task
            modelContext.insert(progress)
        }

        // 导入录音文件
        for recordingData in taskData.recordings {
            if let audioData = recordingData.audioData {
                do {
                    let fileName = "\(task.id.uuidString)_\(recordingData.date.timeIntervalSince1970).m4a"
                    let recordingURL = recordingStorage.urlForSaving(filename: fileName)
                    try audioData.write(to: recordingURL)

                    let recording = RecordingItem(
                        date: recordingData.date,
                        fileUrl: fileName
                    )
                    recording.isFavorite = recordingData.isFavorite
                    recording.note = recordingData.note
                    recording.task = task
                    modelContext.insert(recording)
                } catch {
                    print("❌ 导入录音文件失败: \(error)")
                }
            }
        }

        // 导入视频文件
        for videoData in taskData.videos {
            if let videoFileData = videoData.videoData {
                do {
                    let fileName = "\(task.id.uuidString)_\(videoData.date.timeIntervalSince1970).mp4"
                    let videoURL = recordingStorage.urlForSaving(filename: fileName)
                    try videoFileData.write(to: videoURL)

                    let video = VideoItem(
                        date: videoData.date,
                        fileUrl: fileName
                    )
                    video.task = task
                    modelContext.insert(video)
                } catch {
                    print("❌ 导入视频文件失败: \(error)")
                }
            }
        }

        try modelContext.save()
    }

    private func importCollection(_ collectionData: CollectionBackupData, allTasks: [TaskBackupData]) async throws {
        // 检查是否已存在相同ID的收藏集
        let existingCollectionDescriptor = FetchDescriptor<CollectionItem>(predicate: #Predicate { $0.id == collectionData.id })
        let existingCollections = try modelContext.fetch(existingCollectionDescriptor)

        if !existingCollections.isEmpty {
            throw NSError(domain: "ImportError", code: 2002, userInfo: [NSLocalizedDescriptionKey: "收藏集已存在"])
        }

        // 创建新收藏集
        let collection = CollectionItem(name: "")
        collection.id = collectionData.id
        collection.name = collectionData.name
        collection.desc = collectionData.desc
        collection.order = collectionData.order
        collection.type = CollectionType(rawValue: collectionData.type) ?? .userCreated
        collection.createdDate = collectionData.createdDate
        collection.lastModified = collectionData.lastModified

        // 导入封面图片
        if let coverImageData = collectionData.coverImageData {
            do {
                let fileName = "\(collection.id.uuidString)_collection_cover.jpg"
                let imageURL = imageManager.storage.urlForSaving(filename: fileName)
                try coverImageData.write(to: imageURL)
                collection.cover = fileName
            } catch {
                print("❌ 导入收藏集封面图片失败: \(error)")
            }
        }

        modelContext.insert(collection)

        // 关联任务（只关联已成功导入的任务）
        var associatedTasks: [TaskItem] = []
        for taskId in collectionData.taskIds {
            let taskDescriptor = FetchDescriptor<TaskItem>(predicate: #Predicate { $0.id == taskId })
            if let existingTask = try modelContext.fetch(taskDescriptor).first {
                associatedTasks.append(existingTask)
            }
        }
        collection.taskItems = associatedTasks

        try modelContext.save()
    }

    // MARK: - Utility Functions

    func getBackupFileType() -> UTType {
        return UTType(filenameExtension: "zip") ?? UTType.zip
    }

    func generateBackupFileName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let dateString = formatter.string(from: Date())
        return "OnlyPractice_Backup_\(dateString).zip"
    }

    // MARK: - ZIP Creation Helpers

    private func createZipArchive(jsonData: Data, mediaFiles: [MediaFileInfo]) throws -> URL {
        let tempDir = FileManager.default.temporaryDirectory
        let zipURL = tempDir.appendingPathComponent(generateBackupFileName())

        // 删除已存在的文件
        if FileManager.default.fileExists(atPath: zipURL.path) {
            try FileManager.default.removeItem(at: zipURL)
        }

        // 创建ZIP文件
        let coordinator = NSFileCoordinator()
        var error: NSError?

        coordinator.coordinate(writingItemAt: zipURL, options: [], error: &error) { (url) in
            do {
                // 创建临时目录结构
                let tempBackupDir = tempDir.appendingPathComponent("backup_temp")
                try FileManager.default.createDirectory(at: tempBackupDir, withIntermediateDirectories: true)

                // 写入JSON文件
                let jsonURL = tempBackupDir.appendingPathComponent("backup.json")
                try jsonData.write(to: jsonURL)

                // 创建媒体文件目录
                let imagesDir = tempBackupDir.appendingPathComponent("images")
                let recordingsDir = tempBackupDir.appendingPathComponent("recordings")
                let videosDir = tempBackupDir.appendingPathComponent("videos")

                try FileManager.default.createDirectory(at: imagesDir, withIntermediateDirectories: true)
                try FileManager.default.createDirectory(at: recordingsDir, withIntermediateDirectories: true)
                try FileManager.default.createDirectory(at: videosDir, withIntermediateDirectories: true)

                // 复制媒体文件
                for mediaFile in mediaFiles {
                    let destinationDir: URL
                    switch mediaFile.type {
                    case .image:
                        destinationDir = imagesDir
                    case .recording:
                        destinationDir = recordingsDir
                    case .video:
                        destinationDir = videosDir
                    }

                    let destinationURL = destinationDir.appendingPathComponent(mediaFile.fileName)

                    // 如果目标文件已存在，先删除
                    if FileManager.default.fileExists(atPath: destinationURL.path) {
                        try FileManager.default.removeItem(at: destinationURL)
                    }

                    try FileManager.default.copyItem(at: mediaFile.sourceURL, to: destinationURL)
                }

                // 创建ZIP文件
                try self.zipDirectory(at: tempBackupDir, to: url)

                // 清理临时目录
                try FileManager.default.removeItem(at: tempBackupDir)

                // 清理临时媒体文件
                self.cleanupTemporaryFiles(mediaFiles)

            } catch {
                print("❌ 创建ZIP文件失败: \(error)")
            }
        }

        if let error = error {
            throw error
        }

        return zipURL
    }

    private func zipDirectory(at sourceURL: URL, to destinationURL: URL) throws {
        // 使用简化的文件复制方法，创建一个包含所有文件的目录结构
        // 然后使用 tar 格式（iOS 支持）而不是 ZIP

        // 创建一个临时的 tar.gz 文件
        let tarURL = destinationURL.appendingPathExtension("tar.gz")

        // 使用 FileManager 来创建一个简单的归档
        // 这里我们先实现一个基本版本，将所有文件复制到一个目录中
        let tempArchiveDir = FileManager.default.temporaryDirectory.appendingPathComponent("archive_\(UUID().uuidString)")
        try FileManager.default.createDirectory(at: tempArchiveDir, withIntermediateDirectories: true)

        // 递归复制所有文件到归档目录
        try copyDirectoryContents(from: sourceURL, to: tempArchiveDir)

        // 创建一个简单的 ZIP 文件（使用 Data 方式）
        try createSimpleZip(from: tempArchiveDir, to: destinationURL)

        // 清理临时目录
        try FileManager.default.removeItem(at: tempArchiveDir)
    }

    private func copyDirectoryContents(from sourceURL: URL, to destinationURL: URL) throws {
        let items = try FileManager.default.contentsOfDirectory(at: sourceURL, includingPropertiesForKeys: [.isDirectoryKey], options: [])

        for item in items {
            let resourceValues = try item.resourceValues(forKeys: [.isDirectoryKey])
            let destinationItem = destinationURL.appendingPathComponent(item.lastPathComponent)

            if resourceValues.isDirectory == true {
                // 如果是目录，创建目录并递归复制内容
                try FileManager.default.createDirectory(at: destinationItem, withIntermediateDirectories: true)
                try copyDirectoryContents(from: item, to: destinationItem)
            } else {
                // 如果是文件，直接复制
                try FileManager.default.copyItem(at: item, to: destinationItem)
            }
        }
    }

    private func createSimpleZip(from sourceDir: URL, to zipURL: URL) throws {
        // 简化版本：创建一个包含所有文件数据的简单格式
        // 这不是真正的 ZIP 格式，但可以用于备份和恢复

        var archiveData = Data()
        var fileIndex: [String: [String: Any]] = [:]

        // 递归收集所有文件
        try collectAllFiles(from: sourceDir, baseURL: sourceDir, archiveData: &archiveData, fileIndex: &fileIndex)

        // 将索引信息序列化并添加到末尾
        let indexData = try JSONSerialization.data(withJSONObject: fileIndex)
        let indexSize = Int32(indexData.count)

        archiveData.append(indexData)

        // 使用安全的方法写入Int32，避免内存对齐问题
        let sizeBytes = [
            UInt8(indexSize & 0xFF),
            UInt8((indexSize >> 8) & 0xFF),
            UInt8((indexSize >> 16) & 0xFF),
            UInt8((indexSize >> 24) & 0xFF)
        ]
        archiveData.append(Data(sizeBytes))

        try archiveData.write(to: zipURL)
    }

    private func collectAllFiles(from currentDir: URL, baseURL: URL, archiveData: inout Data, fileIndex: inout [String: [String: Any]]) throws {
        let items = try FileManager.default.contentsOfDirectory(at: currentDir, includingPropertiesForKeys: [.isDirectoryKey], options: [])

        for item in items {
            let resourceValues = try item.resourceValues(forKeys: [.isDirectoryKey])

            if resourceValues.isDirectory == true {
                // 递归处理子目录
                try collectAllFiles(from: item, baseURL: baseURL, archiveData: &archiveData, fileIndex: &fileIndex)
            } else {
                // 处理文件
                let fileData = try Data(contentsOf: item)
                let relativePath = String(item.path.dropFirst(baseURL.path.count + 1))

                fileIndex[relativePath] = [
                    "offset": archiveData.count,
                    "size": fileData.count
                ]
                archiveData.append(fileData)
            }
        }
    }

    struct MediaFileInfo {
        let sourceURL: URL
        let fileName: String
        let type: MediaFileType

        enum MediaFileType {
            case image, recording, video
        }
    }

    // MARK: - ZIP Extraction Helpers

    private func extractZipFile(_ zipURL: URL) async throws -> BackupData {
        await MainActor.run {
            currentImportStatus = "正在解压备份文件..."
        }

        let tempDir = FileManager.default.temporaryDirectory
        let extractDir = tempDir.appendingPathComponent("backup_extract_\(UUID().uuidString)")

        // 创建解压目录
        try FileManager.default.createDirectory(at: extractDir, withIntermediateDirectories: true)

        // 解压ZIP文件
        try unzipFile(at: zipURL, to: extractDir)

        // 读取JSON数据
        let jsonURL = extractDir.appendingPathComponent("backup.json")
        guard FileManager.default.fileExists(atPath: jsonURL.path) else {
            throw NSError(domain: "BackupError", code: 1003, userInfo: [NSLocalizedDescriptionKey: "备份文件中缺少数据文件"])
        }

        let jsonData = try Data(contentsOf: jsonURL)
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        do {
            let backupData = try decoder.decode(BackupData.self, from: jsonData)

            // 恢复媒体文件
            try await restoreMediaFiles(from: extractDir, backupData: backupData)

            // 清理临时目录
            try FileManager.default.removeItem(at: extractDir)

            return backupData
        } catch {
            // 清理临时目录
            try? FileManager.default.removeItem(at: extractDir)
            throw NSError(domain: "BackupError", code: 1002, userInfo: [NSLocalizedDescriptionKey: "备份文件格式错误或已损坏: \(error.localizedDescription)"])
        }
    }

    private func unzipFile(at zipURL: URL, to destinationURL: URL) throws {
        // 解压我们的简单归档格式
        let archiveData = try Data(contentsOf: zipURL)
        print("📦 开始解压归档文件，大小: \(archiveData.count) 字节")

        // 读取索引大小（最后4字节）
        guard archiveData.count >= 4 else {
            throw NSError(domain: "ZipError", code: 1, userInfo: [NSLocalizedDescriptionKey: "归档文件格式错误：文件太小"])
        }

        let indexSizeData = archiveData.suffix(4)

        // 使用安全的方法读取Int32，避免内存对齐问题
        let indexSize: Int32
        if indexSizeData.count == 4 {
            let bytes = Array(indexSizeData)
            indexSize = Int32(bytes[0]) |
                       (Int32(bytes[1]) << 8) |
                       (Int32(bytes[2]) << 16) |
                       (Int32(bytes[3]) << 24)
        } else {
            throw NSError(domain: "ZipError", code: 1, userInfo: [NSLocalizedDescriptionKey: "归档文件格式错误：索引大小数据不完整"])
        }
        print("📦 索引大小: \(indexSize) 字节")

        // 读取索引数据
        let indexSizeInt = Int(indexSize)

        // 检查索引大小的合理性
        guard indexSizeInt > 0 && indexSizeInt < archiveData.count else {
            throw NSError(domain: "ZipError", code: 2, userInfo: [NSLocalizedDescriptionKey: "归档文件索引大小异常: \(indexSizeInt)"])
        }

        guard archiveData.count >= indexSizeInt + 4 else {
            throw NSError(domain: "ZipError", code: 2, userInfo: [NSLocalizedDescriptionKey: "归档文件索引损坏：需要 \(indexSizeInt + 4) 字节，实际 \(archiveData.count) 字节"])
        }

        let indexStart = archiveData.count - indexSizeInt - 4
        let indexData = archiveData.subdata(in: indexStart..<(indexStart + indexSizeInt))

        // 解析索引
        guard let fileIndex = try JSONSerialization.jsonObject(with: indexData) as? [String: [String: Any]] else {
            throw NSError(domain: "ZipError", code: 3, userInfo: [NSLocalizedDescriptionKey: "无法解析归档索引"])
        }

        // 创建目标目录
        try FileManager.default.createDirectory(at: destinationURL, withIntermediateDirectories: true)

        // 提取文件
        for (relativePath, info) in fileIndex {
            guard let offset = info["offset"] as? Int, let size = info["size"] as? Int else { continue }

            let fileData = archiveData.subdata(in: offset..<(offset + size))
            let fileURL = destinationURL.appendingPathComponent(relativePath)

            // 创建文件的父目录（如果需要）
            let parentDir = fileURL.deletingLastPathComponent()
            try FileManager.default.createDirectory(at: parentDir, withIntermediateDirectories: true)

            try fileData.write(to: fileURL)
        }
    }

    private func restoreMediaFiles(from extractDir: URL, backupData: BackupData) async throws {
        await MainActor.run {
            currentImportStatus = "正在恢复媒体文件..."
        }

        let imageStorage = imageManager.storage
        let imagesDir = extractDir.appendingPathComponent("images")
        let recordingsDir = extractDir.appendingPathComponent("recordings")
        let videosDir = extractDir.appendingPathComponent("videos")

        // 恢复图片文件
        if FileManager.default.fileExists(atPath: imagesDir.path) {
            let imageFiles = try FileManager.default.contentsOfDirectory(at: imagesDir, includingPropertiesForKeys: nil)
            for imageFile in imageFiles {
                let destinationURL = imageStorage.urlForSaving(filename: imageFile.lastPathComponent)

                // 如果文件已存在，先删除
                if FileManager.default.fileExists(atPath: destinationURL.path) {
                    try FileManager.default.removeItem(at: destinationURL)
                }

                try FileManager.default.copyItem(at: imageFile, to: destinationURL)
            }
        }

        // 录音和视频文件在导入任务时会直接从备份数据中恢复，这里不需要处理
    }
}
