//
//  RecordingManager.swift
//  practice
//
//  Created by <PERSON> on 2025/4/16.
//

import Foundation
import AVFoundation
import SwiftData

class RecordingManager: NSObject, ObservableObject {
    let storage: RecordingStorageProvider

    private var audioRecorder: AVAudioRecorder?
    private var audioPlayer: AVAudioPlayer?
    private var modelContext: ModelContext

    @Published var recordingTime: TimeInterval = 0
    
    private var recordingStartDate: Date?
    private var accumulatedRecordingTime: TimeInterval = 0
    
    @Published var isPlaying = false {
        didSet {
            if isPlaying {
                startPlaybackProgressTimer()
            } else {
                endPlaybackProgressTimer()
            }
        }
    }
    @Published var playbackTime: TimeInterval = 0
    @Published var status: RecordingStatus = .notStart
    
    @Published var powerLevels: [Float] = [] // 动态展示声波
    private let maxSamples = 100  // 显示的点数
    
    var lastRecordingURL: URL?
    var lastPlayingURL: URL?
    
    private var timer: Timer?
    private var playbackTimer: Timer?
    
    var playbackDuration: TimeInterval {
        audioPlayer?.duration ?? 1
    }

    func toggleFavorite(of recordingItem: RecordingItem) {
        recordingItem.isFavorite.toggle()
    }
    
    func deleteRecording(_ recording: RecordingItem) {
        do {
            // 删除文件
//            let fileURL = resolveRecordingURL(recording.fileUrl)
            // 从数据库中删除记录
            print("on delete", iCloudDocumentManager.shared.icloudAvailable)
            print(recording.fileUrl)
            modelContext.delete(recording)
            
            try storage.deleteFile(at: recording.fileUrl)
            try? modelContext.save()
            print("✅ Deleted recording: \(recording.fileUrl)")
        } catch {
            print("❌ Failed to delete recording: \(error)")
        }
    }
    
    func playRecording(from url: URL) {
        let session = AVAudioSession.sharedInstance()
        do {
            if (lastPlayingURL == url && audioPlayer != nil) {
                // resume
            } else {
                try session.setCategory(.playback, mode: .default)
                try session.setActive(true)
                
                audioPlayer = try AVAudioPlayer(contentsOf: url)
                audioPlayer?.delegate = self
                lastPlayingURL = url
            }
            audioPlayer?.play()
            isPlaying = true
        } catch {
            print("Playback failed: \(error)")
        }
    }
    
    func seekPlayback(to time: TimeInterval) {
        audioPlayer?.currentTime = time
        isPlaying = true
        audioPlayer?.play()
        playbackTime = time
    }
    
    static func defaultStorageProvider() -> RecordingStorageProvider {
        let icloudManager = iCloudDocumentManager.shared
        return icloudManager.icloudAvailable ? ICloudStorageProvider() : LocalStorageProvider()
    }
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
        self.storage = RecordingManager.defaultStorageProvider()
        print("aaaaaa", AVAudioSession.sharedInstance().availableInputs)
        super.init()
    }
    
    func startRecording(pieceId: String) {
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.mixWithOthers, .defaultToSpeaker])
            try audioSession.setActive(true)
            
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let recordingName = "\(pieceId)-\(Date().timeIntervalSince1970).m4a"
            let audioFilename = documentsPath.appendingPathComponent(recordingName)
            
            let settings = [
                AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
                AVSampleRateKey: 44100,
                AVNumberOfChannelsKey: 2,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]
            
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.record()
            audioRecorder?.isMeteringEnabled = true
            
            status = .inProgress
            recordingStartDate = Date()
            startMeteringTimer()
        } catch {
            print("Could not start recording: \(error.localizedDescription)")
        }
    }
    
    func stopRecording(pieceId: String) {
        audioRecorder?.stop()
        status = .finished
        recordingStartDate = nil
        timer?.invalidate()
        timer = nil
        if let url = audioRecorder?.url {
            lastRecordingURL = url
        }
    }
    
    func saveRecording(task: TaskItem, url sourceURL: URL) {
        // 目标文件名
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        // 清洗曲目名，去除非法文件名字符（如 / \ : * ? " < > |）
        let sanitizedTitle = task.pieceName
            .replacingOccurrences(of: "[\\\\/:*?\"<>|]", with: "-", options: .regularExpression)
        let dateString = formatter.string(from: Date())
        let fileName = "\(sanitizedTitle)-\(dateString).m4a"
        let destinationURL = storage.urlForSaving(filename: fileName)

        do {
            try FileManager.default.copyItem(at: sourceURL, to: destinationURL)

            // 存储文件路径而不是绝对 URL
            let recording = RecordingItem(
                date: Date(),
                fileUrl: "\(fileName)" // 保存相对路径
            )
            task.recordings?.append(recording)
            modelContext.insert(recording)
            try? modelContext.save()
            print("✅Saved recording at: \(destinationURL.path), fileURL: \(fileName)")
        } catch {
            print("❌ Failed to save recording: \(error)")
        }
    }
    
    func importRecording(from sourceURL: URL, to task: TaskItem) async throws {
        let didAccess = sourceURL.startAccessingSecurityScopedResource()
        let attributes = try FileManager.default.attributesOfItem(atPath: sourceURL.path)
        let creationDate = (attributes[.creationDate] as? Date) ?? Date()
        defer {
            if didAccess {
                sourceURL.stopAccessingSecurityScopedResource()
            }
        }
        let allowedExtensions = ["m4a", "aac", "mp3", "wav"]

        guard allowedExtensions.contains(sourceURL.pathExtension.lowercased()) else {
            throw NSError(domain: "Invalid file type", code: 1001, userInfo: nil)
        }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"

        let sanitizedTitle = task.pieceName
            .replacingOccurrences(of: "[\\\\/:*?\"<>|]", with: "-", options: .regularExpression)

        let dateString = formatter.string(from: Date())
        let newFileName = "\(sanitizedTitle)-imported-\(dateString).\(sourceURL.pathExtension)"
        let destinationURL = storage.urlForSaving(filename: newFileName)

        do {
            try FileManager.default.copyItem(at: sourceURL, to: destinationURL)

            let recording = RecordingItem(
                date: creationDate,
                fileUrl: newFileName
            )

            task.recordings?.append(recording)
            modelContext.insert(recording)
            try modelContext.save()

            print("✅ Imported recording from \(sourceURL.lastPathComponent) to \(newFileName)")
        } catch {
            print("❌ Failed to import recording: \(error)")
            throw error
        }
    }

    func extractAudioFromVideo(videoURL: URL, to task: TaskItem, at createDate: Date) async throws {
        let didAccess = videoURL.startAccessingSecurityScopedResource()

        defer {
            if didAccess {
                videoURL.stopAccessingSecurityScopedResource()
            }
        }

        let asset = AVURLAsset(url: videoURL)

        // 检查视频是否有音频轨道
        let audioTracks = try await asset.loadTracks(withMediaType: .audio)
        guard !audioTracks.isEmpty else {
            throw NSError(domain: "No audio track found in video", code: 1002, userInfo: nil)
        }

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"

        let sanitizedTitle = task.pieceName
            .replacingOccurrences(of: "[\\\\/:*?\"<>|]", with: "-", options: .regularExpression)

        let dateString = formatter.string(from: Date())
        let fileName = "\(sanitizedTitle)-video-audio-\(dateString).m4a"
        let destinationURL = storage.urlForSaving(filename: fileName)

        print("🔍 DEBUG: Generated filename: \(fileName)")
        print("🔍 DEBUG: Destination URL: \(destinationURL)")

        // 创建导出会话
        print("🔍 DEBUG: Creating export session...")
        guard let exportSession = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetAppleM4A) else {
            print("❌ DEBUG: Failed to create export session")
            throw NSError(domain: "Failed to create export session", code: 1003, userInfo: nil)
        }

        print("🔍 DEBUG: Export session created successfully")
        print("🔍 DEBUG: Supported file types: \(exportSession.supportedFileTypes)")

        exportSession.outputURL = destinationURL
        exportSession.outputFileType = .m4a

        // 只导出音频
        exportSession.audioMix = nil

        print("🔍 DEBUG: Starting export...")
        await exportSession.export()
        print("🔍 DEBUG: Export completed with status: \(exportSession.status.rawValue)")

        switch exportSession.status {
        case .completed:
            print("✅ DEBUG: Export completed successfully")
            print("🔍 DEBUG: Output file exists: \(FileManager.default.fileExists(atPath: destinationURL.path))")

            let recording = RecordingItem(
                date: createDate,
                fileUrl: fileName
            )

            task.recordings?.append(recording)
            modelContext.insert(recording)
            try modelContext.save()

            print("✅ Extracted audio from video to \(fileName)")

        case .failed:
            print("❌ DEBUG: Export failed")
            if let error = exportSession.error {
                print("❌ Export failed: \(error)")
                if let error = error as NSError? {
                    print("❌ Error domain: \(error.domain)")
                    print("❌ Error code: \(error.code)")
                    print("❌ Error userInfo: \(error.userInfo)")
                }
                throw error
            } else {
                print("❌ Export failed with unknown error")
                throw NSError(domain: "Export failed with unknown error", code: 1004, userInfo: nil)
            }

        case .cancelled:
            print("❌ DEBUG: Export was cancelled")
            throw NSError(domain: "Export was cancelled", code: 1005, userInfo: nil)

        default:
            print("❌ DEBUG: Unexpected export status: \(exportSession.status.rawValue)")
            throw NSError(domain: "Unexpected export status", code: 1006, userInfo: nil)
        }
    }
    
    func pauseRecording() {
        guard status == .inProgress else { return } // 只有录制中才允许暂停
        audioRecorder?.pause()
        status = .pause
        // 停止声波采样定时器
        timer?.invalidate()
        timer = nil
    }
    
    func resumeRecording() {
        guard status == .pause else { return }
        audioRecorder?.record()
        status = .inProgress
        recordingStartDate = Date() - recordingTime
        startMeteringTimer()
    }
    
    func discardRecording() {
        if let urlString = lastRecordingURL?.absoluteString, let url = URL(string: urlString) {
            try? FileManager.default.removeItem(at: url)
            lastRecordingURL = nil
        }
        status = .notStart
        resetRecord()
    }
    
    private func startPlaybackProgressTimer() {
        // 如果已有 Timer，先停掉
        playbackTimer?.invalidate()
        
        playbackTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] timer in
            guard let self = self,
                  let player = self.audioPlayer,
                  player.isPlaying else {
                timer.invalidate()
                self?.playbackTimer = nil
                return
            }

            DispatchQueue.main.async {
                self.playbackTime = player.currentTime
            }
        }
    }
    
    private func startMeteringTimer() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.audioRecorder?.updateMeters()
            if let start = self.recordingStartDate {
                recordingTime = Date().timeIntervalSince(start)
            }
            let power = self.audioRecorder?.averagePower(forChannel: 0) ?? -160

            let normalized = max(0, (power + 160) / 160 * 100) // 0 ~ 100
            let enhanced = pow(normalized, 4) / pow(100, 4)  // 0 ~ 1  显著提升中高值视觉
            self.powerLevels.append(max(0.05, enhanced))
//            self.powerLevels.append(normalized)

            if self.powerLevels.count > self.maxSamples {
                self.powerLevels.removeFirst()
            }
        }
    }
    
    private func endPlaybackProgressTimer() {
        playbackTimer?.invalidate()
        playbackTimer = nil
    }
    
    
    func stopPlayback() {
        audioPlayer?.stop()
        isPlaying = false
    }
    
    func resetRecord() {
        // 处理录制一半，放弃录音
        audioRecorder?.stop()
        audioRecorder = nil
        
        // 停止播放
        audioPlayer?.stop()
        audioPlayer = nil
        
        // 清理状态
        isPlaying = false
        status = .notStart
        recordingTime = 0
        playbackTime = 0
        powerLevels = []
        lastRecordingURL = nil
        lastPlayingURL = nil
        
        // 停掉所有 timer
        timer?.invalidate()
        timer = nil
        playbackTimer?.invalidate()
        playbackTimer = nil
        
//        try? AVAudioSession.sharedInstance().setActive(false)
    }
    
    enum RecordingStatus: String {
        case notStart
        case inProgress
        case pause
        case finished
    }
    
    func cleanOrphanRecordings(for task: TaskItem) {
        // 只删除与指定任务相关联的录音文件
        guard let recordings = task.recordings else { return }

        for recording in recordings {
            if !recording.fileUrl.isEmpty {
                do {
                    try storage.deleteFile(at: recording.fileUrl)
                    print("🗑 删除任务相关录音文件: \(recording.fileUrl)")
                } catch {
                    print("❌ 删除录音文件失败: \(error)")
                }
            }
        }
    }
    
    func getFileSize(url: URL) -> String {
        do {
            let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
            if let size = resourceValues.fileSize {
                let sizeInMB = Double(size) / (1024.0 * 1024.0)
                return String(format: "%.2f MB", sizeInMB)
            }
        } catch {
            print("❌ Failed to get file size: \(error)")
        }
        return "Unknown size"
    }
    
    func resolveRecordingURL(_ relativePath: String) -> URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
            .appendingPathComponent(relativePath)
    }
    
    @MainActor
    func getDurationFromUrl(_ url: URL) async -> TimeInterval? {
        let asset = AVURLAsset(url: url)
        do {
            let duration = try await asset.load(.duration)
            return CMTimeGetSeconds(duration)
        } catch {
            print("❌ Failed to load duration: \(error)")
            return nil
        }
    }
}

extension RecordingManager: AVAudioRecorderDelegate {
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            print("Recording failed")
        }
    }
}

extension RecordingManager: AVAudioPlayerDelegate {
    func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        isPlaying = false
        playbackTime = player.duration
    }
}
