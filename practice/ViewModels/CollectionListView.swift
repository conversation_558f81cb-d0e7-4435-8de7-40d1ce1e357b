//
//  CollectionListView.swift
//  practice
//
//  Created by Augment Agent on 2024/12/19.
//

import SwiftUI
import SwiftData

struct CollectionListView: View {
    @EnvironmentObject private var collectionManager: CollectionManager
    @EnvironmentObject private var navManager: NavigationManager
    @EnvironmentObject private var imageManager: ImageManager
    
    @State private var showingCreateCollection = false
    @State private var selectedCollection: CollectionItem?
    @State private var showingDeleteAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerView
                
                // Collections List
                collectionsListView
            }
            .navigationTitle("Achievement")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        showingCreateCollection = true
                    } label: {
                        Image(systemName: "plus")
                            .imageScale(.large)
                            .frame(width: 44, height: 44, alignment: .center)
                    }
                }
            }
            .sheet(isPresented: $showingCreateCollection, onDismiss: {
                selectedCollection = nil
            }) {
                if let selectedCollection = selectedCollection {
                    CreateCollectionView(editingCollection: selectedCollection)
                } else {
                    CreateCollectionView()
                }
            }
            .confirmationDialog(
                "Delete Collection",
                isPresented: $showingDeleteAlert,
                titleVisibility: .visible,
                presenting: selectedCollection
            ) { collection in
                Button("Delete", role: .destructive) {
                    collectionManager.deleteCollection(collection)
                }
                Button("Cancel", role: .cancel) { }
            } message: { collection in
                Text("Are you sure you want to delete \"\(collection.name)\"? The achievements will not be deleted.")
            }
        }
        .onAppear {
            collectionManager.loadData()
        }
    }
    
    private var headerView: some View {
        VStack(spacing: 16) {
            // All Achievements Collection (Default)
            CollectionCardView(
                collection: collectionManager.getAllAchievementsCollection(),
                isDefault: true
            )
            .onTapGesture {
                navManager.pushState(item: .collectionDetailPage(collectionManager.getAllAchievementsCollection()))
            }
        }
        .padding(.horizontal)
        .padding(.top, 8)
    }
    
    private var collectionsListView: some View {
        List {
            if collectionManager.collections.isEmpty {
                emptyStateView
            } else {
                ForEach(collectionManager.collections, id: \.id) { collection in
                    CollectionCardView(collection: collection)
                        .listRowInsets(EdgeInsets())
                        .listRowSeparator(.hidden)
                        .padding(.vertical, 8)
                        .onTapGesture {
                            navManager.pushState(item: .collectionDetailPage(collection))
                        }
                        .swipeActions(edge: .trailing) {
                            Button {
                                selectedCollection = collection
                                showingCreateCollection = true
                            } label: {
                                Label("Edit", systemImage: "square.and.pencil")
                            }
                            .tint(.indigo)
                            
                            Button {
                                selectedCollection = collection
                                showingDeleteAlert = true
                            } label: {
                                Label("Delete", systemImage: "trash.fill")
                            }
                            .tint(.red)
                        }
                }
                .onMove { source, destination in
                    collectionManager.reorderCollections(from: source, to: destination)
                }
            }
        }
        .listStyle(.inset)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Image(systemName: "folder.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Collections Yet")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text("Create your first collection to organize your achievements")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button {
                showingCreateCollection = true
            } label: {
                Text("Create Collection")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.accentColor)
                    .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .listRowSeparator(.hidden)
        .listRowBackground(Color.clear)
    }
}

struct CollectionCardView: View {
    let collection: CollectionItem
    var isDefault: Bool = false
    
    @EnvironmentObject private var imageManager: ImageManager
    
    var body: some View {
        HStack(spacing: 16) {
            // Cover Image
            coverImageView
            
            // Collection Info
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text(collection.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if isDefault {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption)
                    }
                }
                
                Text("\(collection.achievementCount) achievements")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                if !isDefault {
                    Text("Updated \(collection.lastModified, style: .relative) ago")
                        .font(.caption)
                        .foregroundColor(.tertiary)
                }
            }
            
            Spacer()
            
            // Chevron
            Image(systemName: "chevron.right")
                .font(.caption)
                .foregroundColor(.tertiary)
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var coverImageView: some View {
        Group {
            if !collection.cover.isEmpty {
                CloudImageView(path: collection.cover)
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 60, height: 60)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            } else {
                // Default cover with first achievement image or placeholder
                if let firstTask = collection.taskItems?.first,
                   let coverPath = firstTask.coverImagePath {
                    CloudImageView(path: coverPath)
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 60, height: 60)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.tertiarySystemBackground))
                        .frame(width: 60, height: 60)
                        .overlay(
                            Image(systemName: "music.note")
                                .font(.title2)
                                .foregroundColor(.secondary)
                        )
                }
            }
        }
    }
}

#Preview {
    let modelContext = TaskItemSchemaV1.container.mainContext

    CollectionListView()
        .environmentObject(CollectionManager.shared)
        .environmentObject(NavigationManager.shared)
        .environmentObject(ImageManager(modelContext: modelContext))
}
