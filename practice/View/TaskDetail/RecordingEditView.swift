//
//  RecordingEditView.swift
//  practice
//
//  Created by <PERSON> on 2025/6/25.
//

import SwiftUI

struct RecordingEditView: View {
    let recording: RecordingItem
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    // Form state
    @State private var editedDate: Date
    @State private var editedNote: String
    
    // UI state
    @State private var focusedField: FocusField? = nil

    enum FocusField {
        case date
    }
    
    init(recording: RecordingItem) {
        self.recording = recording
        self._editedDate = State(initialValue: recording.date)
        self._editedNote = State(initialValue: recording.note)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            headerView
            
            // Form Content
            ScrollView {
                VStack(spacing: 24) {
                    // Date Section
                    dateSection
                        .padding(.horizontal)
                    
                    // Note Section
                    noteSection
                        .padding(.horizontal)
                }
                .padding(.vertical)
            }
        }
    }
    
    // MARK: - Header View
    private var headerView: some View {
        ZStack {
            Text("Edit Recording")
                .font(.title3)
                .fontWeight(.semibold)
            
            HStack {
                Button("Cancel") {
                    dismiss()
                }
                Spacer()
                <PERSON><PERSON>("Save") {
                    saveChanges()
                }
                .disabled(false) // 可以保存空的note
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Date Section
    private var dateSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Date")
                .font(.headline)
                .foregroundColor(.primary)
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    focusedField = focusedField == .date ? nil : .date
                }
            }) {
                HStack {
                    Text(editedDate.formatted(date: .abbreviated, time: .shortened))
                        .foregroundColor(.primary)
                    Spacer()
                    Image(systemName: "calendar")
                        .foregroundColor(.accentColor)
                }
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
            }
            .buttonStyle(.plain)
            
            if focusedField == .date {
                DatePicker(
                    "Select Date",
                    selection: $editedDate,
                    in: ...Date(),
                    displayedComponents: [.date, .hourAndMinute]
                )
                .datePickerStyle(.graphical)
                .labelsHidden()
                .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
    }
    
    // MARK: - Note Section
    private var noteSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Note")
                .font(.headline)
                .foregroundColor(.primary)
            
            TextEditor(text: $editedNote)
                .scrollContentBackground(.hidden)
                .frame(minHeight: 100)
                .padding()
                .background(Color.accentColor.opacity(0.1))
                .cornerRadius(10)
        }
    }
    
    // MARK: - Actions
    private func saveChanges() {
        recording.date = editedDate
        recording.note = editedNote
        
        do {
            try modelContext.save()
            dismiss()
        } catch {
            print("Failed to save recording changes: \(error)")
        }
    }
}

#Preview {
    let recording = RecordingItem(date: Date(), fileUrl: "test.m4a")
    recording.note = "Test note"
    
    return RecordingEditView(recording: recording)
        .modelContainer(for: RecordingItem.self, inMemory: true)
}
