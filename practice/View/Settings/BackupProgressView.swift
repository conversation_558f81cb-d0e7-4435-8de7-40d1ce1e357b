//
//  BackupProgressView.swift
//  practice
//
//  Created by Augment Agent on 2025/6/28.
//

import SwiftUI
import UniformTypeIdentifiers

struct BackupProgressView: View {
    @ObservedObject var backupManager: BackupManager
    @Binding var isPresented: Bool
    @State private var showDocumentPicker = false
    @State private var backupURL: URL?
    @State private var showShareSheet = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                VStack(spacing: 10) {
                    Image(systemName: "externaldrive.badge.plus")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("数据备份")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                .padding(.top, 40)
                
                Spacer()
                
                // 进度区域
                VStack(spacing: 20) {
                    // 进度条
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("备份进度")
                                .font(.headline)
                            Spacer()
                            Text("\(Int(backupManager.backupProgress * 100))%")
                                .font(.headline)
                                .foregroundColor(.blue)
                        }
                        
                        ProgressView(value: backupManager.backupProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            .scaleEffect(y: 2)
                    }
                    .padding(.horizontal, 20)
                    
                    // 当前状态
                    Text(backupManager.currentBackupStatus)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                .padding(.vertical, 30)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color(.systemGray6))
                )
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 按钮区域
                VStack(spacing: 16) {
                    if backupManager.isBackingUp {
                        // 备份中，显示取消按钮
                        Button("取消备份") {
                            isPresented = false
                        }
                        .font(.headline)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red, lineWidth: 2)
                        )
                    } else if backupURL != nil {
                        // 备份完成，显示保存按钮
                        Button("保存备份文件") {
                            showDocumentPicker = true
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                        
                        Button("分享备份文件") {
                            showShareSheet = true
                        }
                        .font(.headline)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.blue, lineWidth: 2)
                        )
                    } else {
                        // 初始状态或失败，显示开始备份按钮
                        Button("开始备份") {
                            startBackup()
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                        .disabled(backupManager.isBackingUp)
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        isPresented = false
                    }
                    .disabled(backupManager.isBackingUp)
                }
            }
        }
        .fileExporter(
            isPresented: $showDocumentPicker,
            document: backupURL.map { BackupDocument(url: $0) },
            contentType: .json,
            defaultFilename: backupManager.generateBackupFileName()
        ) { result in
            switch result {
            case .success(let url):
                print("✅ 备份文件已保存到: \(url)")
                isPresented = false
            case .failure(let error):
                print("❌ 保存备份文件失败: \(error)")
            }
        }
        .sheet(isPresented: $showShareSheet) {
            if let url = backupURL {
                ShareSheet(activityItems: [url])
            }
        }
    }
    
    private func startBackup() {
        Task {
            do {
                let url = try await backupManager.startBackup()
                await MainActor.run {
                    backupURL = url
                }
            } catch {
                print("❌ 备份失败: \(error)")
            }
        }
    }
}

// 备份文档类型
struct BackupDocument: FileDocument {
    static var readableContentTypes: [UTType] { [.json] }
    
    let url: URL
    
    init(url: URL) {
        self.url = url
    }
    
    init(configuration: ReadConfiguration) throws {
        throw CocoaError(.fileReadCorruptFile)
    }
    
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = try Data(contentsOf: url)
        return FileWrapper(regularFileWithContents: data)
    }
}

// 分享表单
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    BackupProgressView(
        backupManager: BackupManager(),
        isPresented: .constant(true)
    )
}
