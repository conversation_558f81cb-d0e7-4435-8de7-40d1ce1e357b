//
//  ImportProgressView.swift
//  practice
//
//  Created by Augment Agent on 2025/6/28.
//

import SwiftUI
import UniformTypeIdentifiers

struct ImportProgressView: View {
    @ObservedObject var backupManager: BackupManager
    @Binding var isPresented: Bool
    @State private var showDocumentPicker = false
    @State private var showResultAlert = false
    @State private var showKeepDataAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                VStack(spacing: 10) {
                    Image(systemName: "square.and.arrow.down")
                        .font(.system(size: 60))
                        .foregroundColor(.green)
                    
                    Text("数据导入")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                }
                .padding(.top, 40)
                
                Spacer()
                
                if backupManager.isImporting {
                    // 导入进度区域
                    VStack(spacing: 20) {
                        // 进度条
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("导入进度")
                                    .font(.headline)
                                Spacer()
                                Text("\(Int(backupManager.importProgress * 100))%")
                                    .font(.headline)
                                    .foregroundColor(.green)
                            }
                            
                            ProgressView(value: backupManager.importProgress)
                                .progressViewStyle(LinearProgressViewStyle(tint: .green))
                                .scaleEffect(y: 2)
                        }
                        .padding(.horizontal, 20)
                        
                        // 当前状态
                        Text(backupManager.currentImportStatus)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 20)
                    }
                    .padding(.vertical, 30)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                    .padding(.horizontal, 20)
                } else if let results = backupManager.importResults {
                    // 导入结果区域
                    ImportResultsView(results: results)
                        .padding(.horizontal, 20)
                } else {
                    // 选择文件提示
                    VStack(spacing: 20) {
                        Image(systemName: "doc.badge.plus")
                            .font(.system(size: 80))
                            .foregroundColor(.gray)
                        
                        Text("请选择备份文件")
                            .font(.title2)
                            .foregroundColor(.secondary)
                        
                        Text("支持 .zip 格式的备份文件")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 40)
                }
                
                Spacer()
                
                // 按钮区域
                VStack(spacing: 16) {
                    if backupManager.isImporting {
                        // 导入中，显示取消按钮
                        Button("取消导入") {
                            isPresented = false
                        }
                        .font(.headline)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red, lineWidth: 2)
                        )
                    } else if backupManager.importResults != nil {
                        // 导入完成，显示完成按钮
                        Button("完成") {
                            isPresented = false
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green)
                        )
                    } else {
                        // 初始状态，显示选择文件按钮
                        Button("选择备份文件") {
                            showDocumentPicker = true
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green)
                        )
                    }
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 40)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        isPresented = false
                    }
                    .disabled(backupManager.isImporting)
                }
            }
        }
        .fileImporter(
            isPresented: $showDocumentPicker,
            allowedContentTypes: [.zip],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                if let url = urls.first {
                    startImport(from: url)
                }
            case .failure(let error):
                print("❌ 选择文件失败: \(error)")
            }
        }
        .onChange(of: backupManager.importResults) { _, results in
            if let results = results {
                if results.failedTasks > 0 || results.failedCollections > 0 {
                    showKeepDataAlert = true
                } else {
                    showResultAlert = true
                }
            }
        }
        .alert("导入完成", isPresented: $showResultAlert) {
            Button("确定") {
                // 关闭弹窗
            }
        } message: {
            if let results = backupManager.importResults {
                Text("成功导入 \(results.successfulTasks) 项任务和 \(results.successfulCollections) 个收藏集！")
            }
        }
        .alert("导入部分成功", isPresented: $showKeepDataAlert) {
            Button("保留已导入内容") {
                // 保留数据
            }
            Button("重新导入", role: .destructive) {
                // TODO: 实现重新导入逻辑
            }
        } message: {
            if let results = backupManager.importResults {
                Text("已成功导入 \(results.successfulTasks) 项任务，失败 \(results.failedTasks) 项。\n已成功导入 \(results.successfulCollections) 个收藏集，失败 \(results.failedCollections) 个。\n\n是否保留已导入的内容？")
            }
        }
    }
    
    private func startImport(from url: URL) {
        Task {
            do {
                try await backupManager.startImport(from: url)
            } catch {
                print("❌ 导入失败: \(error)")
            }
        }
    }
}

// 导入结果视图
struct ImportResultsView: View {
    let results: BackupManager.ImportResults
    
    var body: some View {
        VStack(spacing: 20) {
            // 成功图标
            if results.failedTasks == 0 && results.failedCollections == 0 {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.green)
            } else {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.orange)
            }
            
            // 结果统计
            VStack(spacing: 12) {
                HStack {
                    Text("任务:")
                        .font(.headline)
                    Spacer()
                    Text("成功 \(results.successfulTasks), 失败 \(results.failedTasks)")
                        .foregroundColor(results.failedTasks > 0 ? .orange : .green)
                }
                
                HStack {
                    Text("收藏集:")
                        .font(.headline)
                    Spacer()
                    Text("成功 \(results.successfulCollections), 失败 \(results.failedCollections)")
                        .foregroundColor(results.failedCollections > 0 ? .orange : .green)
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            
            // 错误详情（如果有）
            if !results.errors.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("错误详情:")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    ScrollView {
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(results.errors, id: \.self) { error in
                                Text("• \(error)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .frame(maxHeight: 100)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            }
        }
    }
}

#Preview {
    ImportProgressView(
        backupManager: BackupManager(),
        isPresented: .constant(true)
    )
}
