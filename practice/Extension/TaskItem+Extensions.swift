//
//  TaskItem+Extensions.swift
//  practice
//
//  Created by Augment Agent on 2024/12/28.
//

import Foundation
import SwiftData

extension TaskItem {
    
    // MARK: - Array Comparison Utilities
    
    /// Compares two arrays of TaskItem for equality based on display-relevant properties
    /// This is useful for optimizing UI updates by avoiding unnecessary state changes
    static func arraysAreEqual(_ lhs: [TaskItem], _ rhs: [TaskItem]) -> Bool {
        // Quick check: different counts
        guard lhs.count == rhs.count else { return false }
        
        // Create dictionaries for efficient lookup by ID
        let lhsDict = Dictionary(uniqueKeysWithValues: lhs.map { ($0.id, $0) })
        let rhsDict = Dictionary(uniqueKeysWithValues: rhs.map { ($0.id, $0) })
        
        // Check if all IDs match
        guard Set(lhsDict.keys) == Set(rhsDict.keys) else { return false }
        
        // Compare each task's key properties
        for (id, leftTask) in lhsDict {
            guard let rightTask = rhsDict[id] else { return false }
            
            // Compare essential properties that would affect UI display
            if leftTask.pieceName != rightTask.pieceName ||
               leftTask.composerName != rightTask.composerName ||
               leftTask.key != rightTask.key ||
               leftTask.difficulty != rightTask.difficulty ||
               leftTask.taskType != rightTask.taskType ||
               leftTask.coverImagePath != rightTask.coverImagePath ||
               leftTask.lastModified != rightTask.lastModified ||
               leftTask.mostRecentPracticeDate != rightTask.mostRecentPracticeDate {
                return false
            }
        }
        
        // Check if the order in the arrays is the same (important for display order)
        for (index, leftTask) in lhs.enumerated() {
            if leftTask.id != rhs[index].id {
                return false
            }
        }
        
        return true
    }
    
    // MARK: - Individual Task Comparison
    
    /// Compares this task with another for display-relevant equality
    func isDisplayEqual(to other: TaskItem) -> Bool {
        return self.id == other.id &&
               self.pieceName == other.pieceName &&
               self.composerName == other.composerName &&
               self.key == other.key &&
               self.difficulty == other.difficulty &&
               self.taskType == other.taskType &&
               self.coverImagePath == other.coverImagePath &&
               self.lastModified == other.lastModified &&
               self.mostRecentPracticeDate == other.mostRecentPracticeDate
    }
}
